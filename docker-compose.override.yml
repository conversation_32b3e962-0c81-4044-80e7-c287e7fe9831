version: '3.8'

services:
  haobbs:
    # 在开发环境中挂载源代码目录
    volumes:
      # 挂载数据库文件以实现数据持久化
      - ./forum.db:/app/forum.db
      # 挂载静态文件目录以便于开发时更新
      - ./static:/app/static
      # 挂载模板目录以便于开发时更新
      - ./templates:/app/templates
      # 挂载应用代码目录以支持热重载
      - ./app.py:/app/app.py
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=1
      - PORT=5002
      - SECRET_KEY=your-secret-key-here
    command: ["flask", "run", "--host=0.0.0.0", "--port=5002", "--reload", "--debugger"]