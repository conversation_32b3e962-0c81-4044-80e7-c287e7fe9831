@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义组件样式 */
@layer components {
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded transition-all duration-200;
  }
  
  .btn-success {
    @apply bg-success-500 hover:bg-success-600 text-white px-4 py-2 rounded transition-all duration-200;
  }
  
  .btn-danger {
    @apply bg-danger-500 hover:bg-danger-600 text-white px-4 py-2 rounded transition-all duration-200;
  }
  
  .card {
    @apply bg-white rounded-lg p-6 shadow-card hover:shadow-card-hover transition-all duration-200;
  }
  
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center;
  }
  
  .modal-content {
    @apply bg-white rounded-lg p-6 max-w-2xl w-full mx-4 shadow-modal;
  }

  .tag-item {
    @apply inline-flex items-center px-5 py-2 rounded-full bg-gradient-to-br from-gray-100 to-gray-200 text-gray-700 no-underline transition-all duration-300 border border-gray-300 shadow-sm font-medium relative overflow-hidden;
  }

  .tag-item:hover {
    @apply from-gray-200 to-gray-300 -translate-y-0.5 shadow-md;
  }

  .tag-item.active {
    @apply from-primary-500 to-primary-600 text-white border-primary-700 shadow-lg font-semibold;
  }

  .tag-count {
    @apply ml-2 px-2 py-1 bg-black bg-opacity-10 rounded-xl text-xs font-semibold;
  }

  .tag-item.active .tag-count {
    @apply bg-white bg-opacity-25;
  }
  
  /* 侧边栏分类项样式 */
  .sidebar-category-item {
    @apply block px-4 py-3 mb-2 rounded-lg text-gray-700 no-underline transition-all duration-200;
  }
  
  .sidebar-category-item:hover {
    @apply bg-gray-200;
  }
  
  .sidebar-category-item.active {
    @apply bg-primary-500 text-white;
  }
  
  .sidebar-category-item .tag-count {
    @apply float-right bg-black bg-opacity-10 rounded-full px-2 py-1 text-xs;
  }
  
  .sidebar-category-item.active .tag-count {
    @apply bg-white bg-opacity-25;
  }

  /* 分页控件样式 */
  .pagination {
    @apply flex justify-center items-center gap-2 mt-6 mb-4;
  }

  .page-link {
    @apply px-3 py-2 text-sm border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 no-underline;
  }

  .page-link.active {
    @apply bg-primary-500 text-white border-primary-500 hover:bg-primary-600 hover:border-primary-600;
  }

  /* 编辑和删除按钮样式 */
  .btn-edit {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300 rounded transition-all duration-200 cursor-pointer no-underline;
  }

  .btn-edit:hover {
    @apply text-gray-900 border-gray-400;
  }

  .btn-delete {
    @apply bg-red-50 hover:bg-red-100 text-red-600 border border-red-200 rounded transition-all duration-200 cursor-pointer;
  }

  .btn-delete:hover {
    @apply text-red-700 border-red-300 bg-red-100;
  }

  /* 固定回复框样式组件 */
  .fixed-reply-container {
    @apply fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50;
    padding: 16px;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
  }

  .fixed-reply-box {
    @apply max-w-3xl mx-auto;
  }

  .reply-form {
    @apply space-y-3;
  }

  .reply-form textarea {
    @apply w-full border border-gray-300 rounded-lg px-4 py-3 text-sm resize-y focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200;
    min-height: 80px;
  }

  .reply-form .btn-primary {
    @apply text-sm;
  }

  /* 移动端适配 */
  @media (max-width: 768px) {
    .fixed-reply-container {
      padding: 12px;
    }

    .reply-form textarea {
      min-height: 60px;
    }
  }
}

/* 保留必要的自定义样式 */
@layer utilities {
  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 移动端特定样式 */
  @media (max-width: 768px) {
    .mobile-nav-hidden {
      display: none;
    }

    .mobile-scroll-x {
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
    }

    .mobile-full-width {
      width: 100% !important;
      text-align: center;
    }
  }
}
