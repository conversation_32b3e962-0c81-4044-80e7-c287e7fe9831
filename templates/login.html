<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>登录 - HaoBBS</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#007bff">
    <link rel="icon" href="{{ url_for('static', filename='images/favicon.svg') }}" type="image/svg+xml">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/output.css') }}">
</head>
<body class="font-sans m-0 p-0 leading-relaxed bg-gradient-to-br from-blue-50 to-gray-50 min-h-screen flex items-center justify-center">
    <div class="w-full max-w-md px-4 py-8">
        <!-- Logo/Brand Section -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">欢迎回来</h1>
            <p class="text-gray-600">登录您的HaoBBS账户</p>
        </div>

        <div class="bg-white p-8 rounded-xl shadow-card border border-gray-100">
            <form action="{{ url_for('login') }}" method="post">
            {% with messages = get_flashed_messages() %}
                {% if messages %}
                    {% for message in messages %}
                        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-5 text-sm font-medium">{{ message }}</div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="mb-5">
                <label for="username" class="block mb-2 text-gray-700 font-medium">用户名</label>
                <input type="text" id="username" name="username" required 
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg text-base transition-all duration-200 
                              bg-white focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20
                              placeholder-gray-400"
                       placeholder="请输入用户名">
            </div>
            <div class="mb-6">
                <label for="password" class="block mb-2 text-gray-700 font-medium">密码</label>
                <input type="password" id="password" name="password" required 
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg text-base transition-all duration-200 
                              bg-white focus:outline-none focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20
                              placeholder-gray-400"
                       placeholder="请输入密码">
            </div>
            <button type="submit" 
                    class="w-full bg-primary-500 hover:bg-primary-600 text-white font-bold py-4 px-8 rounded-xl 
                           transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5
                           focus:outline-none focus:ring-4 focus:ring-primary-500/30 focus:ring-offset-2
                           shadow-md transform">
                登录
            </button>
        </form>
    </div>
</div>
</body>
</html>
