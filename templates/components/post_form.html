<!-- 帖子表单共享组件 -->
<style>
    /* 确保body和主要容器有足够高度 */
    body {
        min-height: 100vh;
        margin: 0;
        display: flex;
        flex-direction: column;
    }
    .max-w-4xl.mx-auto {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;
        width: 100%;
        max-width: 56rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    .form-label {
        display: block;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.5rem;
        font-size: 0.8125rem;
    }
    .form-input {
        width: 100%;
        padding: 0.625rem 0.875rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        font-size: 14px;
        transition: all 0.2s ease;
        background-color: #f9fafb;
    }
    .form-input:focus {
        outline: none;
        border-color: #3b82f6;
        background-color: #ffffff;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    .form-input::placeholder {
        color: #9ca3af;
    }
    .form-textarea {
        min-height: 300px;
        resize: vertical;
    }
.btn-group {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
}
    .btn-secondary {
        padding: 0.75rem 1.5rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        background-color: #ffffff;
        color: #374151;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    .btn-secondary:hover {
        background-color: #f3f4f6;
        border-color: #9ca3af;
    }

    /* 水平布局样式 */
    .form-row {
        display: flex;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }
    .form-row .form-group {
        margin-bottom: 0;
    }
    .flex-1 {
        flex: 1;
    }

    /* 响应式设计 - 移动端适配 */
    @media (max-width: 768px) {
        .form-row {
            flex-direction: column;
        }
    }

    /* 内容区域扩展优化 */
    .form-container {
        display: flex;
        flex-direction: column;
        flex: 1;
    }
    form {
        display: flex;
        flex-direction: column;
        flex: 1;
    }
    .content-group {
        flex: 1;
        display: flex;
        flex-direction: column;
    }
    .form-textarea {
        flex: 1;
        resize: vertical;
    }

    /* 字数统计样式 */
    .character-count {
        font-size: 0.75rem;
        color: #6b7280;
        font-weight: 400;
    }
    .character-count.highlight {
        color: #3b82f6;
        font-weight: 500;
    }
    
    /* 选中统计样式 */
    .selection-count {
        font-size: 0.75rem;
        color: #8b5cf6;
        font-weight: 400;
        margin-right: 0.5rem;
        padding-right: 0.5rem;
        border-right: 1px solid #e5e7eb;
    }
    .selection-count.highlight {
        color: #7c3aed;
        font-weight: 500;
    }
    .selection-count.empty {
        color: #9ca3af;
    }
</style>

<script>
    // 添加一些快捷键支持
    document.addEventListener('DOMContentLoaded', function() {
        const textarea = document.getElementById('content');
        const charCount = document.getElementById('character-count');
        const selectionCount = document.getElementById('selection-count');

        // 字符统计函数（只统计中文、英文和数字）
        function countAllCharacters(text) {
            // 使用正则表达式匹配中文、英文和数字字符
            const validChars = text.match(/[\u4e00-\u9fa5a-zA-Z0-9]/g);
            return validChars ? validChars.length : 0;
        }

        // 更新字数统计
        function updateCharacterCount() {
            const text = textarea.value;
            const count = countAllCharacters(text);
            charCount.textContent = count + ' 字';
        }

        // 更新选中统计
        function updateSelectionCount() {
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            
            if (start === end) {
                // 没有选中文本
                selectionCount.textContent = '选中: 0 字';
                selectionCount.classList.add('empty');
                selectionCount.classList.remove('highlight');
            } else {
                // 有选中文本，统计其中的字符
                const selectedText = textarea.value.substring(start, end);
                const count = countAllCharacters(selectedText);
                selectionCount.textContent = '选中: ' + count + ' 字';
                selectionCount.classList.remove('empty');
                
                // 如果选中文本包含字符，高亮显示
                if (count > 0) {
                    selectionCount.classList.add('highlight');
                } else {
                    selectionCount.classList.remove('highlight');
                }
            }
        }

        if (textarea && charCount && selectionCount) {
            // 初始化字数统计
            updateCharacterCount();
            updateSelectionCount();

            // 监听输入事件实时更新字数
            textarea.addEventListener('input', updateCharacterCount);
            textarea.addEventListener('propertychange', updateCharacterCount); // 兼容IE

            // 监听选中相关事件
            textarea.addEventListener('select', updateSelectionCount);
            textarea.addEventListener('selectionchange', updateSelectionCount);
            textarea.addEventListener('mouseup', updateSelectionCount);
            textarea.addEventListener('keyup', updateSelectionCount);

            textarea.addEventListener('keydown', function(e) {
                // Ctrl+B 加粗
                if (e.ctrlKey && e.key === 'b') {
                    e.preventDefault();
                    insertMarkdown('**', '**');
                }
                // Ctrl+I 斜体
                if (e.ctrlKey && e.key === 'i') {
                    e.preventDefault();
                    insertMarkdown('*', '*');
                }
                // Tab键插入4个空格
                if (e.key === 'Tab') {
                    e.preventDefault();
                    insertText('    ');
                }
            });

            function insertMarkdown(before, after) {
                const start = textarea.selectionStart;
                const end = textarea.selectionEnd;
                const selectedText = textarea.value.substring(start, end);
                const newText = before + selectedText + after;

                textarea.value = textarea.value.substring(0, start) + newText + textarea.value.substring(end);
                textarea.selectionStart = start + before.length;
                textarea.selectionEnd = start + before.length + selectedText.length;
                textarea.focus();
                updateCharacterCount(); // 更新字数统计
                updateSelectionCount(); // 更新选中统计
            }

            function insertText(text) {
                const start = textarea.selectionStart;
                textarea.value = textarea.value.substring(0, start) + text + textarea.value.substring(start);
                textarea.selectionStart = textarea.selectionEnd = start + text.length;
                textarea.focus();
                updateCharacterCount(); // 更新字数统计
                updateSelectionCount(); // 更新选中统计
            }

            // 设置默认发布时间（如果是新帖子且没有提供时间）
            const createdAtInput = document.getElementById('created_at');
            if (createdAtInput && !createdAtInput.value) {
                const now = new Date();
                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                const seconds = String(now.getSeconds()).padStart(2, '0');
                
                createdAtInput.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            }
        }
    });
</script>

<!-- 表单容器 -->
<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 md:p-8 form-container">
    <form action="{{ form_action }}" method="post">
        <!-- 发布时间、分类、标题水平布局 -->
        <div class="form-row">
            <!-- 自定义发布时间 - 自适应宽度 -->
            <div class="form-group flex-none">
                <label class="form-label">发布时间（可选）</label>
                <input type="text" name="created_at" id="created_at" 
                       class="form-input" placeholder="YYYY-MM-DD HH:MM:SS"
                       value="{{ created_at_value }}" style="min-width: 180px;">
            </div>

            <!-- 分类 - 自适应宽度 -->
            <div class="form-group flex-none">
                <label class="form-label">分类</label>
                <input type="text" name="category" id="category" required 
                       class="form-input" placeholder="请输入分类名称" value="{{ category_value }}" style="min-width: 120px;">
            </div>

            <!-- 标题 - 占据剩余宽度 -->
            <div class="form-group flex-1">
                <label class="form-label">帖子标题</label>
                <input type="text" name="title" id="title" required 
                       class="form-input" placeholder="请输入帖子标题" value="{{ title_value }}">
            </div>
        </div>

        <!-- 内容 -->
        <div class="form-group content-group">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                <label class="form-label">内容</label>
                <div style="display: flex; align-items: center;">
                    <span id="selection-count" class="selection-count empty">选中: 0 字</span>
                    <span id="character-count" class="character-count">0 字</span>
                </div>
            </div>
            <textarea name="content" id="content" required 
                      class="form-input form-textarea" 
                      placeholder="支持 Markdown 语法，请输入帖子内容...">{{ content_value }}</textarea>
        </div>

        <!-- 按钮组 -->
        <div class="btn-group">
            <a href="{{ url_for('index') }}" class="btn-secondary">取消</a>
            <button type="submit" class="btn-success">{{ submit_button_text }}</button>
        </div>
    </form>
</div>
