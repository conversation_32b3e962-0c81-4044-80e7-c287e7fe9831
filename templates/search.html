<!DOCTYPE html>
<html>

<head>
    <title>搜索结果 - 我的论坛</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="{{ url_for('static', filename='images/favicon.svg') }}" type="image/svg+xml">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/output.css') }}">
    <!-- Markdown样式 -->
    <style>
        .markdown-preview {
            color: #6b7280;
            line-height: 1.5;
        }
        .markdown-preview h1, .markdown-preview h2, .markdown-preview h3,
        .markdown-preview h4, .markdown-preview h5, .markdown-preview h6 {
            margin: 0;
            font-weight: 600;
            color: #374151;
        }
        .markdown-preview p { margin: 0.3em 0; }
        .markdown-preview code {
            background-color: #f3f4f6;
            padding: 0.1em 0.3em;
            border-radius: 2px;
            font-size: 0.85em;
        }
        .markdown-preview strong { font-weight: normal; color: #374151; }
        .markdown-preview em { font-style: italic; }
        
        .search-highlight {
            background-color: #ffeb3b;
            padding: 0 2px;
            border-radius: 2px;
        }
    </style>
</head>

<body class="font-sans m-0 p-0 leading-relaxed">
    <!-- 引入侧边栏模块 -->
    {% include 'components/sidebar.html' %}
    
    <!-- 主内容区域 -->
    <div class="main-content" id="mainContent">
        <div class="w-[95%] max-w-3xl mx-auto my-5 px-4 md:w-full md:my-8 md:px-6">

            <!-- 搜索结果标题 -->
            <div class="mb-6">
                <h2 class="text-2xl font-semibold text-gray-900 mb-2">
                    搜索结果
                    {% if search_query %}
                    <span class="text-primary-600">"{{ search_query }}"</span>
                    {% endif %}
                </h2>
                <p class="text-gray-600">
                    {% if posts %}
                        找到 {{ posts|length }} 个相关帖子
                    {% else %}
                        没有找到相关帖子
                    {% endif %}
                </p>
            </div>

            <!-- 搜索结果列表 -->
            {% if posts %}
            {% for post in posts %}
            <div class="card mb-4 border border-gray-200 hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5">
                <div class="flex flex-col gap-3">
                    <div class="flex justify-between items-start gap-4">
                        <h3 class="m-0 text-lg leading-snug">
                            <a href="{{ url_for('view_post', post_id=post.id) }}" class="text-gray-900 no-underline hover:text-primary-500 transition-colors duration-200">
                                {{ post.title }}
                            </a>
                        </h3>
                        <div class="flex items-center gap-2">
                            <span class="bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded-full">
                                {{ post.category }}
                            </span>
                            {% if post.match_type == 'reply' %}
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                匹配回复
                            </span>
                            {% else %}
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                匹配帖子
                            </span>
                            {% endif %}
                            <time class="text-gray-500 text-sm whitespace-nowrap flex-shrink-0">{{ post.created_at | beijing_time }}</time>
                        </div>
                    </div>

                    <!-- 内容摘要（高亮搜索关键词） -->
                    {% if post.content %}
                    <div class="text-ellipsis-2 text-gray-600 m-0 text-sm leading-relaxed markdown-preview">
                        {{ post.content | markdown_truncate(150, true, '...') | safe }}
                    </div>
                    <div class="text-gray-500 text-xs" style="text-align: right;">
                        字数：{{ post.content|word_count }} 字
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
            {% else %}
            <!-- 无结果提示 -->
            <div class="text-center py-12">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">没有找到相关帖子</h3>
                <p class="text-gray-600 mb-4">尝试使用不同的关键词或检查拼写</p>
                <a href="{{ url_for('index') }}" class="btn-primary">返回首页</a>
            </div>
            {% endif %}

        </div>
    </div>
</body>

</html>
