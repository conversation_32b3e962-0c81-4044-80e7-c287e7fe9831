version: '3.8'

services:
  haobbs:
    build: .
    ports:
      - "5002:5002"
    volumes:
      # 挂载数据库文件以实现数据持久化
      - ./forum.db:/app/forum.db
      # 挂载静态文件目录以便于开发时更新
      - ./static:/app/static
      # 挂载模板目录以便于开发时更新
      - ./templates:/app/templates
    environment:
      - FLASK_ENV=development
      - PORT=5002
      - SECRET_KEY=your-secret-key-here  # 在生产环境中应使用更安全的密钥
    restart: unless-stopped
    networks:
      - haobbs-network

  # 开发模式下的CSS构建服务
  css-builder:
    build:
      context: .
      dockerfile: Dockerfile.dev
    volumes:
      # 挂载CSS源文件
      - ./static/css:/app/static/css
      # 挂载Tailwind配置
      - ./tailwind.config.js:/app/tailwind.config.js
      - ./postcss.config.js:/app/postcss.config.js
    command: npm run build-css
    profiles:
      - dev

networks:
  haobbs-network:
    driver: bridge
