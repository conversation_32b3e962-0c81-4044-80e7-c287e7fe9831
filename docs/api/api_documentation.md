# HaoBBS API 文档

## 概述

HaoBBS 是一个基于 Flask 的轻量级论坛系统，提供帖子发布、回复、搜索等功能。本文档详细描述了所有可用的 API 端点。

## 认证机制

HaoBBS 使用基于会话的认证系统。用户需要先通过 `/login` 端点进行登录，成功后会设置会话 cookie（`session_5002`），后续请求需要携带此 cookie 以验证身份。

- **会话有效期**: 7天
- **会话名称**: `session_5002`（端口号可能变化）
- **认证装饰器**: `@login_required` 保护需要登录的端点

## 端点详情

### 1. 静态文件服务

#### 获取静态图片
- **URL**: `/static/images/<path:filename>`
- **方法**: GET
- **认证**: 不需要
- **参数**:
  - `filename` (路径参数): 图片文件名
- **响应**: 图片文件，带有缓存头（缓存1年）
- **描述**: 提供静态图片资源访问

### 2. 用户认证

#### 用户登录
- **URL**: `/login`
- **方法**: GET, POST
- **认证**: 不需要
- **GET 请求**: 返回登录页面
- **POST 请求**: 提交登录表单
  - **表单数据**:
    - `username` (字符串, 必需): 用户名
    - `password` (字符串, 必需): 密码
  - **成功响应**: 重定向到首页 (`/`)
  - **失败响应**: 返回登录页面并显示错误信息
- **描述**: 处理用户登录认证

#### 用户登出
- **URL**: `/logout`
- **方法**: GET
- **认证**: 需要登录
- **响应**: 清除会话并重定向到登录页面
- **描述**: 终止用户会话

### 3. 帖子管理

#### 获取帖子列表
- **URL**: `/`
- **方法**: GET
- **认证**: 需要登录
- **查询参数**:
  - `category` (字符串, 可选): 按分类筛选帖子
- **响应**: 返回首页 HTML，包含帖子列表和分类信息
- **描述**: 显示所有帖子，支持按分类筛选

#### 新建帖子页面
- **URL**: `/new_post`
- **方法**: GET
- **认证**: 需要登录
- **响应**: 返回新建帖子表单页面
- **描述**: 显示创建新帖子的表单

#### 创建新帖子
- **URL**: `/post`
- **方法**: POST
- **认证**: 需要登录
- **表单数据**:
  - `title` (字符串, 必需): 帖子标题
  - `content` (字符串, 必需): 帖子内容（Markdown格式）
  - `category` (字符串, 可选): 帖子分类，默认为"默认分类"
- **响应**: 重定向到首页 (`/`)
- **描述**: 创建新帖子

#### 查看特定帖子
- **URL**: `/post/<int:post_id>`
- **方法**: GET
- **认证**: 需要登录
- **路径参数**:
  - `post_id` (整数, 必需): 帖子ID
- **查询参数**:
  - `page` (整数, 可选): 回复页码，默认为1
  - `sort` (字符串, 可选): 回复排序方式，`asc`（升序）或 `desc`（降序），默认为`desc`
- **响应**: 返回帖子详情页面，包含帖子和分页的回复
- **描述**: 查看特定帖子的详细内容和回复

#### 编辑帖子
- **URL**: `/post/<int:post_id>/edit`
- **方法**: GET, POST
- **认证**: 需要登录
- **路径参数**:
  - `post_id` (整数, 必需): 帖子ID
- **GET 请求**: 返回帖子编辑表单
- **POST 请求**: 提交帖子编辑
  - **表单数据**:
    - `title` (字符串, 必需): 帖子标题
    - `content` (字符串, 必需): 帖子内容
    - `category` (字符串, 可选): 帖子分类
  - **普通响应**: 重定向到帖子详情页
  - **AJAX 响应**: 返回 JSON 格式的更新后帖子数据
- **描述**: 编辑现有帖子内容

#### 删除帖子
- **URL**: `/post/<int:post_id>/delete`
- **方法**: POST
- **认证**: 需要登录
- **路径参数**:
  - `post_id` (整数, 必需): 帖子ID
- **响应**: JSON 格式的响应
  - 成功: `{"success": true}`
  - 失败（有回复）: `{"success": false, "message": "请先删除所有回复后再删除帖子"}`, 状态码 400
- **描述**: 删除帖子（仅当没有回复时）

### 4. 回复管理

#### 创建回复
- **URL**: `/reply/<int:post_id>`
- **方法**: POST
- **认证**: 需要登录
- **路径参数**:
  - `post_id` (整数, 必需): 帖子ID
- **表单数据**:
  - `content` (字符串, 必需): 回复内容（Markdown格式）
- **响应**: JSON 格式的响应 `{"success": true, "replyId": 新回复ID}`
- **描述**: 为指定帖子创建回复

#### 编辑回复
- **URL**: `/reply/<int:reply_id>/edit`
- **方法**: GET, POST
- **认证**: 需要登录
- **路径参数**:
  - `reply_id` (整数, 必需): 回复ID
- **GET 请求**: 返回回复编辑表单（AJAX 请求返回原始内容）
- **POST 请求**: 提交回复编辑
  - **表单数据**:
    - `content` (字符串, 必需): 回复内容
  - **普通响应**: 重定向到帖子详情页
  - **AJAX 响应**: 返回 JSON 格式的更新后回复数据
- **描述**: 编辑现有回复内容

#### 删除回复
- **URL**: `/reply/<int:reply_id>/delete`
- **方法**: POST
- **认证**: 需要登录
- **路径参数**:
  - `reply_id` (整数, 必需): 回复ID
- **响应**: 重定向到帖子详情页
- **描述**: 删除指定回复

### 5. 搜索功能

#### 搜索帖子和回复
- **URL**: `/search`
- **方法**: GET
- **认证**: 需要登录
- **查询参数**:
  - `q` (字符串, 必需): 搜索关键词
  - `category` (字符串, 可选): 按分类筛选搜索结果
- **响应**: 返回搜索结果页面，包含匹配的帖子和回复
- **描述**: 搜索帖子标题、内容和回复内容

## 数据模型

### 帖子 (Post)
```json
{
  "id": "整数，唯一标识",
  "title": "字符串，帖子标题",
  "content": "字符串，帖子内容（Markdown格式）",
  "category": "字符串，帖子分类",
  "created_at": "时间戳，创建时间"
}
```

### 回复 (Reply)
```json
{
  "id": "整数，唯一标识",
  "post_id": "整数，关联的帖子ID",
  "content": "字符串，回复内容（Markdown格式）",
  "created_at": "时间戳，创建时间"
}
```

### 用户 (User)
```json
{
  "id": "整数，唯一标识",
  "username": "字符串，用户名",
  "password_hash": "字符串，密码哈希值",
  "created_at": "时间戳，创建时间"
}
```

## 错误处理

### HTTP 状态码
- `200 OK`: 请求成功
- `302 Found`: 重定向
- `400 Bad Request`: 请求参数错误（如删除有回复的帖子）
- `404 Not Found`: 资源不存在（如访问不存在的帖子或回复）

### 错误响应格式
对于 AJAX 请求，错误响应为 JSON 格式：
```json
{
  "success": false,
  "message": "错误描述"
}
```

对于普通请求，错误通过 Flash 消息显示在页面上。

## 注意事项

1. 所有需要登录的端点都使用 `@login_required` 装饰器保护
2. 内容输入支持 Markdown 格式，后端会自动转换为安全的 HTML
3. 时间显示使用北京时间（UTC+8）
4. 搜索功能同时搜索帖子标题、内容和回复内容
5. 删除帖子前需要先删除所有关联的回复

## 示例用法

### 登录示例
```bash
curl -X POST -d "username=admin&password=admin2025" -c cookies.txt http://localhost:5002/login
```

### 创建帖子示例
```bash
curl -X POST -b cookies.txt -d "title=测试帖子&content=这是内容&category=测试分类" http://localhost:5002/post
```

### 获取帖子列表示例
```bash
curl -b cookies.txt http://localhost:5002/
```

### 搜索示例
```bash
curl -b cookies.txt "http://localhost:5002/search?q=关键词&category=分类名"
```

---
*最后更新: 2025-08-24*
