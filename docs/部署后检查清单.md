# 部署后检查清单

本文档用于确保 HaoBBS 项目部署后各项功能正常运行。

## 1. 端口配置检查

### 本地文件检查
- [x] 确认 `app.py` 文件中端口配置为 5002
- [x] 确认 `scripts/run.sh` 脚本中端口配置为 5002

### 服务器配置检查
- [ ] 确认服务器上应用实际监听端口为 5002
- [ ] 确认 Caddy 反向代理配置指向 localhost:5002
- [ ] 确认防火墙允许 5002 端口访问

## 2. 应用服务检查

### PM2 进程检查
- [ ] 确认 haobbs 应用在 PM2 中正常运行
- [ ] 确认 PM2 配置已保存，重启后能自动启动
- [ ] 检查 PM2 日志无错误信息

### 应用功能检查
- [ ] 通过域名访问首页正常
- [ ] 登录功能正常
- [ ] 发布新帖功能正常
- [ ] 回复功能正常
- [ ] 编辑功能正常
- [ ] 删除功能正常

## 3. 数据库检查

### 数据库文件检查
- [ ] 确认 forum.db 文件存在且权限正确
- [ ] 确认数据库表结构完整
- [ ] 确认用户数据未丢失

### 数据库备份检查
- [ ] 确认部署脚本正确备份了数据库
- [ ] 确认备份文件在 ./backup/ 目录下

## 4. 静态资源检查

### CSS 文件检查
- [ ] 确认 static/css/output.css 文件存在
- [ ] 确认页面样式加载正常

### 其他静态资源检查
- [ ] 确认图片等静态资源可正常访问

## 5. 网络连接检查

### 域名解析检查
- [ ] 确认 bbs.haoxueren.com 域名解析正确
- [ ] 确认 Caddy 配置文件正确加载

### 网络访问测试
- [ ] 使用 curl 测试域名访问返回 200 状态码
- [ ] 测试登录接口响应正常

## 6. 安全检查

### 会话安全检查
- [ ] 确认会话管理配置正确
- [ ] 确认 Cookie 安全设置正确

### 输入验证检查
- [ ] 确认 Markdown 渲染安全
- [ ] 确认 XSS 防护机制正常工作

## 7. 性能检查

### 应用性能检查
- [ ] 确认应用启动时间合理
- [ ] 确认页面加载速度正常

### 资源使用检查
- [ ] 确认内存和 CPU 使用率正常
- [ ] 确认磁盘空间充足

## 8. 日志检查

### 应用日志检查
- [ ] 确认应用日志目录存在
- [ ] 确认日志文件可正常写入
- [ ] 检查最近日志无严重错误

### 系统日志检查
- [ ] 检查系统日志无相关错误

## 9. 备份检查

### 自动备份检查
- [ ] 确认数据库自动备份机制正常
- [ ] 确认备份文件完整性

## 10. 监控检查

### 服务监控检查
- [ ] 确认服务器监控正常
- [ ] 确认应用健康检查接口正常

---
*此检查清单应在每次部署后执行，确保应用稳定运行。*
