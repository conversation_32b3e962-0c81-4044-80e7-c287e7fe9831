# 部署脚本修复说明

## 问题描述

在使用 `scripts/deploy.sh` 部署项目时，发现本地的 `forum.db` 数据库文件会覆盖服务器上的数据库文件，导致数据丢失。

## 问题原因

分析 `deploy.sh` 脚本发现，在文件同步阶段使用的 rsync 命令没有将 `forum.db` 文件排除在外：

```bash
rsync -avz --exclude '.git' \
    --exclude '__pycache__' \
    --exclude '*.pyc' \
    --exclude 'venv/' \
    --exclude 'venv_fixed/' \
    --exclude 'venv_new/' \
    --exclude 'backup/' \
    --exclude 'node_modules/' \
    -e "ssh -p $SERVER_PORT" \
    ./ $SERVER_USER@$SERVER_HOST:$REMOTE_DIR/
```

虽然 `.gitignore` 文件中有 `*.db` 规则，但在 rsync 同步时如果没有显式排除，本地的 `forum.db` 文件仍会被上传到服务器，覆盖服务器上的数据库。

## 解决方案

采用双重保护机制：

1. 在 rsync 命令中显式添加 `--exclude 'forum.db'` 和 `--exclude 'forum.db.local'` 参数
2. 在文件同步前临时重命名本地数据库文件，同步完成后再恢复

```bash
# 临时重命名本地数据库文件，避免在同步时覆盖服务器数据库
if [ -f "forum.db" ]; then
    echo_info "临时重命名本地数据库文件..."
    mv forum.db forum.db.local
    LOCAL_DB_MOVED=true
fi

# 同步项目文件（使用 Docker 部署，包含 Docker 相关文件）
echo_info "同步项目文件到服务器..."
rsync -avz --exclude '.git' \
    --exclude '__pycache__' \
    --exclude '*.pyc' \
    --exclude 'venv/' \
    --exclude 'venv_fixed/' \
    --exclude 'venv_new/' \
    --exclude 'backup/' \
    --exclude 'node_modules/' \
    --exclude 'forum.db' \
    --exclude 'forum.db.local' \
    -e "ssh -p $SERVER_PORT" \
    ./ $SERVER_USER@$SERVER_HOST:$REMOTE_DIR/

# 恢复本地数据库文件
if [ "$LOCAL_DB_MOVED" = true ]; then
    echo_info "恢复本地数据库文件..."
    mv forum.db.local forum.db
fi
```

## 部署流程说明

修复后的部署流程确保数据安全：

1. **备份服务器数据库**：首先将服务器上的 `forum.db` 备份到本地 `backup/` 目录
2. **同步项目文件**：同步除数据库外的所有项目文件到服务器
3. **启动服务**：通过 Docker Compose 启动服务，使用卷挂载保持数据库持久化

## 验证

- 检查 `backup/` 目录确认备份功能正常
- 确认 `.gitignore` 文件包含 `*.db` 规则
- 验证 `deploy.sh` 脚本已添加 `--exclude 'forum.db'` 参数

## 预防措施

1. 定期检查备份目录确保备份功能正常
2. 在重要部署前手动备份服务器数据库
3. 考虑将数据库文件存放在专门的目录中管理
