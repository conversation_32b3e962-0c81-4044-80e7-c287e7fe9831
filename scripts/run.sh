#!/bin/bash

# HaoBBS Docker启动脚本
# 使用Docker运行项目，确保环境一致性
#
# 使用方法:
#   chmod +x scripts/run.sh
#   ./scripts/run.sh                 # 开发模式（默认，启用热重载）
#   ./scripts/run.sh --prod          # 生产模式（禁用热重载）

set -e

# 解析命令行参数
PROD_MODE=false
for arg in "$@"
do
    case $arg in
        --prod)
        PROD_MODE=true
        shift
        ;;
    esac
done

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 颜色输出函数
function echo_info() {
    echo -e "\033[34m[INFO] $1\033[0m"
}

function echo_success() {
    echo -e "\033[32m[SUCCESS] $1\033[0m"
}

function echo_error() {
    echo -e "\033[31m[ERROR] $1\033[0m"
}

function echo_warning() {
    echo -e "\033[33m[WARNING] $1\033[0m"
}

# 清理函数
function cleanup() {
    echo ""
    echo_info "应用已停止，再见！"
}

# 设置清理陷阱
trap cleanup EXIT INT TERM

echo_success "=== HaoBBS Docker启动 ==="

cd "$PROJECT_ROOT"

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo_error "Docker未安装，请先安装Docker"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo_error "Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

echo_info "检查Docker环境..."
docker_version=$(docker --version)
docker_compose_version=$(docker-compose --version)
echo_info "Docker版本: $docker_version"
echo_info "Docker Compose版本: $docker_compose_version"

# 停止可能正在运行的容器
echo_info "停止可能正在运行的容器..."
docker-compose down >/dev/null 2>&1 || true

# 根据模式选择启动方式
if [ "$PROD_MODE" = true ]; then
    echo_info "以生产模式启动Docker容器..."
    docker-compose up -d
    echo_info "生产模式已启用：热重载功能已禁用"
else
    echo_info "以开发模式启动Docker容器（启用热重载）..."
    docker-compose -f docker-compose.yml -f docker-compose.override.yml up -d
    echo_info "开发模式已启用：Python代码热重载功能已开启"
fi

# 等待服务启动
echo_info "等待服务启动..."
sleep 5

# 检查服务状态
if docker-compose ps | grep -q "Up"; then
    echo_success "HaoBBS已成功启动！"
    echo_info "应用地址: http://localhost:5002"
    if [ "$PROD_MODE" = false ]; then
        echo_info "开发模式: 已启用热重载功能"
        echo_info "修改Python代码、模板或静态文件后将自动重载"
    else
        echo_info "生产模式: 已禁用热重载功能"
    fi
    echo_info "按 Ctrl+C 停止应用"
    echo ""
    
    # 显示容器日志
    docker-compose logs -f
else
    echo_error "HaoBBS启动失败"
    docker-compose logs
    exit 1
fi