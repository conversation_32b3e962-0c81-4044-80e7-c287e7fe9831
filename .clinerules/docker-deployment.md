## Brief overview
本规则文件包含 HaoBBS 项目的 Docker 部署和容器化指南。它涵盖了在 Docker 容器中构建、运行和管理应用程序的最佳实践。

## Docker 构建过程
- 使用多阶段构建来优化镜像大小
- 将构建依赖项与运行时依赖项分离
- 使用官方基础镜像 (python:3.9-alpine, node:18-alpine)
- 利用 Docker 层缓存来加快构建速度
- 在运行阶段安装系统依赖项如 sqlite

## 容器配置
- 为 Flask 应用程序暴露端口 5002
- 设置环境变量进行配置 (FLASK_ENV, PORT)
- 使用卷挂载实现数据持久化 (forum.db)
- 为开发环境挂载静态文件和模板目录
- 配置适当的工作目录 (/app)

## Docker Compose 设置
- 为主应用定义服务
- 创建自定义网络用于服务通信
- 使用卷挂载实现开发环境热重载
- 为生产环境稳定性设置重启策略
- 在 compose 文件中配置环境变量

## 开发环境 vs 生产环境
- 开发模式：启用调试和自动重载
- 生产模式：禁用调试，优化性能
- 在需要时为特定环境使用不同的 Dockerfiles
- 为每个环境实现适当的日志配置

## 数据持久化
- 将数据库文件 (forum.db) 挂载为卷以实现持久化
- 挂载静态资源用于开发环境更新
- 挂载模板文件用于开发环境热重载
- 在重大更新前备份数据库

## 性能优化
- 在 pip 安装时使用 --no-cache-dir 标志
- 安装后清理包管理器缓存
- 最小化最终镜像中的层数
- 使用 .dockerignore 排除不必要的文件
- 在生产环境中实施适当的资源限制

## 安全考虑
- 尽可能以非 root 用户运行容器
- 定期更新基础镜像
- 扫描镜像中的漏洞
- 在 requirements 中使用特定的包版本
- 避免在 Docker 镜像中存储机密信息

## 故障排除
- 检查容器日志以查找应用程序错误
- 验证端口映射和网络连接
- 确保挂载卷的文件权限正确
- 验证数据库文件访问和权限
- 监控容器资源使用情况
